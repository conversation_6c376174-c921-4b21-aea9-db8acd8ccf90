// Code generated by bpf2go; DO NOT EDIT.
//go:build arm64

package bpf

import (
	"bytes"
	_ "embed"
	"fmt"
	"io"

	"github.com/cilium/ebpf"
)

type GoTlsGoCommonSymaddrsT struct {
	InternalSyscallConn   int64
	TlsConn               int64
	NetTCPConn            int64
	FD_SysfdOffset        int32
	TlsConnConnOffset     int32
	SyscallConnConnOffset int32
	G_goidOffset          int32
	G_addrOffset          int32
	_                     [4]byte
}

type GoTlsGoTlsSymaddrsT struct {
	WriteC_loc      GoTlsLocationT
	WriteB_loc      GoTlsLocationT
	WriteRetval0Loc GoTlsLocationT
	WriteRetval1Loc GoTlsLocationT
	ReadC_loc       GoTlsLocationT
	ReadB_loc       GoTlsLocationT
	ReadRetval0Loc  GoTlsLocationT
	ReadRetval1Loc  GoTlsLocationT
}

type GoTlsLocationT struct {
	Type   GoTlsLocationTypeT
	Offset int32
}

type GoTlsLocationTypeT uint32

const (
	GoTlsLocationTypeTKLocationTypeInvalid   GoTlsLocationTypeT = 0
	GoTlsLocationTypeTKLocationTypeStack     GoTlsLocationTypeT = 1
	GoTlsLocationTypeTKLocationTypeRegisters GoTlsLocationTypeT = 2
)

// LoadGoTls returns the embedded CollectionSpec for GoTls.
func LoadGoTls() (*ebpf.CollectionSpec, error) {
	reader := bytes.NewReader(_GoTlsBytes)
	spec, err := ebpf.LoadCollectionSpecFromReader(reader)
	if err != nil {
		return nil, fmt.Errorf("can't load GoTls: %w", err)
	}

	return spec, err
}

// LoadGoTlsObjects loads GoTls and converts it into a struct.
//
// The following types are suitable as obj argument:
//
//	*GoTlsObjects
//	*GoTlsPrograms
//	*GoTlsMaps
//
// See ebpf.CollectionSpec.LoadAndAssign documentation for details.
func LoadGoTlsObjects(obj interface{}, opts *ebpf.CollectionOptions) error {
	spec, err := LoadGoTls()
	if err != nil {
		return err
	}

	return spec.LoadAndAssign(obj, opts)
}

// GoTlsSpecs contains maps and programs before they are loaded into the kernel.
//
// It can be passed ebpf.CollectionSpec.Assign.
type GoTlsSpecs struct {
	GoTlsProgramSpecs
	GoTlsMapSpecs
}

// GoTlsSpecs contains programs before they are loaded into the kernel.
//
// It can be passed ebpf.CollectionSpec.Assign.
type GoTlsProgramSpecs struct {
	ProbeEntryTlsConnRead   *ebpf.ProgramSpec `ebpf:"probe_entry_tls_conn_read"`
	ProbeEntryTlsConnWrite  *ebpf.ProgramSpec `ebpf:"probe_entry_tls_conn_write"`
	ProbeReturnTlsConnRead  *ebpf.ProgramSpec `ebpf:"probe_return_tls_conn_read"`
	ProbeReturnTlsConnWrite *ebpf.ProgramSpec `ebpf:"probe_return_tls_conn_write"`
}

// GoTlsMapSpecs contains maps before they are loaded into the kernel.
//
// It can be passed ebpf.CollectionSpec.Assign.
type GoTlsMapSpecs struct {
	ActiveSslReadArgsMap  *ebpf.MapSpec `ebpf:"active_ssl_read_args_map"`
	ActiveSslWriteArgsMap *ebpf.MapSpec `ebpf:"active_ssl_write_args_map"`
	ActiveTlsConnOpMap    *ebpf.MapSpec `ebpf:"active_tls_conn_op_map"`
	ConnEvtMap            *ebpf.MapSpec `ebpf:"conn_evt_map"`
	ConnEvtRb             *ebpf.MapSpec `ebpf:"conn_evt_rb"`
	ConnInfoMap           *ebpf.MapSpec `ebpf:"conn_info_map"`
	ControlValues         *ebpf.MapSpec `ebpf:"control_values"`
	FilterMntnsMap        *ebpf.MapSpec `ebpf:"filter_mntns_map"`
	FilterNetnsMap        *ebpf.MapSpec `ebpf:"filter_netns_map"`
	FilterPidMap          *ebpf.MapSpec `ebpf:"filter_pid_map"`
	FilterPidnsMap        *ebpf.MapSpec `ebpf:"filter_pidns_map"`
	FirstPacketEvtMap     *ebpf.MapSpec `ebpf:"first_packet_evt_map"`
	FirstPacketRb         *ebpf.MapSpec `ebpf:"first_packet_rb"`
	GoCommonSymaddrsMap   *ebpf.MapSpec `ebpf:"go_common_symaddrs_map"`
	GoSslUserSpaceCallMap *ebpf.MapSpec `ebpf:"go_ssl_user_space_call_map"`
	GoTlsSymaddrsMap      *ebpf.MapSpec `ebpf:"go_tls_symaddrs_map"`
	Rb                    *ebpf.MapSpec `ebpf:"rb"`
	RegsHeap              *ebpf.MapSpec `ebpf:"regs_heap"`
	SslDataMap            *ebpf.MapSpec `ebpf:"ssl_data_map"`
	SslRb                 *ebpf.MapSpec `ebpf:"ssl_rb"`
	SslUserSpaceCallMap   *ebpf.MapSpec `ebpf:"ssl_user_space_call_map"`
	SyscallDataMap        *ebpf.MapSpec `ebpf:"syscall_data_map"`
	SyscallRb             *ebpf.MapSpec `ebpf:"syscall_rb"`
}

// GoTlsObjects contains all objects after they have been loaded into the kernel.
//
// It can be passed to LoadGoTlsObjects or ebpf.CollectionSpec.LoadAndAssign.
type GoTlsObjects struct {
	GoTlsPrograms
	GoTlsMaps
}

func (o *GoTlsObjects) Close() error {
	return _GoTlsClose(
		&o.GoTlsPrograms,
		&o.GoTlsMaps,
	)
}

// GoTlsMaps contains all maps after they have been loaded into the kernel.
//
// It can be passed to LoadGoTlsObjects or ebpf.CollectionSpec.LoadAndAssign.
type GoTlsMaps struct {
	ActiveSslReadArgsMap  *ebpf.Map `ebpf:"active_ssl_read_args_map"`
	ActiveSslWriteArgsMap *ebpf.Map `ebpf:"active_ssl_write_args_map"`
	ActiveTlsConnOpMap    *ebpf.Map `ebpf:"active_tls_conn_op_map"`
	ConnEvtMap            *ebpf.Map `ebpf:"conn_evt_map"`
	ConnEvtRb             *ebpf.Map `ebpf:"conn_evt_rb"`
	ConnInfoMap           *ebpf.Map `ebpf:"conn_info_map"`
	ControlValues         *ebpf.Map `ebpf:"control_values"`
	FilterMntnsMap        *ebpf.Map `ebpf:"filter_mntns_map"`
	FilterNetnsMap        *ebpf.Map `ebpf:"filter_netns_map"`
	FilterPidMap          *ebpf.Map `ebpf:"filter_pid_map"`
	FilterPidnsMap        *ebpf.Map `ebpf:"filter_pidns_map"`
	FirstPacketEvtMap     *ebpf.Map `ebpf:"first_packet_evt_map"`
	FirstPacketRb         *ebpf.Map `ebpf:"first_packet_rb"`
	GoCommonSymaddrsMap   *ebpf.Map `ebpf:"go_common_symaddrs_map"`
	GoSslUserSpaceCallMap *ebpf.Map `ebpf:"go_ssl_user_space_call_map"`
	GoTlsSymaddrsMap      *ebpf.Map `ebpf:"go_tls_symaddrs_map"`
	Rb                    *ebpf.Map `ebpf:"rb"`
	RegsHeap              *ebpf.Map `ebpf:"regs_heap"`
	SslDataMap            *ebpf.Map `ebpf:"ssl_data_map"`
	SslRb                 *ebpf.Map `ebpf:"ssl_rb"`
	SslUserSpaceCallMap   *ebpf.Map `ebpf:"ssl_user_space_call_map"`
	SyscallDataMap        *ebpf.Map `ebpf:"syscall_data_map"`
	SyscallRb             *ebpf.Map `ebpf:"syscall_rb"`
}

func (m *GoTlsMaps) Close() error {
	return _GoTlsClose(
		m.ActiveSslReadArgsMap,
		m.ActiveSslWriteArgsMap,
		m.ActiveTlsConnOpMap,
		m.ConnEvtMap,
		m.ConnEvtRb,
		m.ConnInfoMap,
		m.ControlValues,
		m.FilterMntnsMap,
		m.FilterNetnsMap,
		m.FilterPidMap,
		m.FilterPidnsMap,
		m.FirstPacketEvtMap,
		m.FirstPacketRb,
		m.GoCommonSymaddrsMap,
		m.GoSslUserSpaceCallMap,
		m.GoTlsSymaddrsMap,
		m.Rb,
		m.RegsHeap,
		m.SslDataMap,
		m.SslRb,
		m.SslUserSpaceCallMap,
		m.SyscallDataMap,
		m.SyscallRb,
	)
}

// GoTlsPrograms contains all programs after they have been loaded into the kernel.
//
// It can be passed to LoadGoTlsObjects or ebpf.CollectionSpec.LoadAndAssign.
type GoTlsPrograms struct {
	ProbeEntryTlsConnRead   *ebpf.Program `ebpf:"probe_entry_tls_conn_read"`
	ProbeEntryTlsConnWrite  *ebpf.Program `ebpf:"probe_entry_tls_conn_write"`
	ProbeReturnTlsConnRead  *ebpf.Program `ebpf:"probe_return_tls_conn_read"`
	ProbeReturnTlsConnWrite *ebpf.Program `ebpf:"probe_return_tls_conn_write"`
}

func (p *GoTlsPrograms) Close() error {
	return _GoTlsClose(
		p.ProbeEntryTlsConnRead,
		p.ProbeEntryTlsConnWrite,
		p.ProbeReturnTlsConnRead,
		p.ProbeReturnTlsConnWrite,
	)
}

func _GoTlsClose(closers ...io.Closer) error {
	for _, closer := range closers {
		if err := closer.Close(); err != nil {
			return err
		}
	}
	return nil
}

// Do not access this directly.
//
//go:embed gotls_arm64_bpfel.o
var _GoTlsBytes []byte
