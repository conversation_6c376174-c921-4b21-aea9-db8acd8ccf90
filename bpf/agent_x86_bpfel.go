// Code generated by bpf2go; DO NOT EDIT.
//go:build 386 || amd64

package bpf

import (
	"bytes"
	_ "embed"
	"fmt"
	"io"

	"github.com/cilium/ebpf"
)

type AgentConnEvtT struct {
	ConnInfo AgentConnInfoT
	ConnType AgentConnTypeT
	_        [4]byte
	Ts       uint64
}

type AgentConnIdS_t struct {
	TgidFd  uint64
	NoTrace AgentConnTraceStateT
	_       [4]byte
}

type AgentConnInfoT struct {
	ConnId struct {
		Upid struct {
			Pid            uint32
			_              [4]byte
			StartTimeTicks uint64
		}
		Fd   int32
		_    [4]byte
		Tsid uint64
	}
	ReadBytes     uint64
	WriteBytes    uint64
	SslReadBytes  uint64
	SslWriteBytes uint64
	Laddr         struct {
		In6 struct {
			Sin6Family   uint16
			Sin6Port     uint16
			Sin6Flowinfo uint32
			Sin6Addr     AgentIn6Addr
			Sin6ScopeId  uint32
		}
	}
	Raddr struct {
		In6 struct {
			Sin6Family   uint16
			Sin6Port     uint16
			Sin6Flowinfo uint32
			Sin6Addr     AgentIn6Addr
			Sin6ScopeId  uint32
		}
	}
	Protocol            AgentTrafficProtocolT
	Role                AgentEndpointRoleT
	PrevCount           uint64
	PrevBuf             [4]int8
	PrependLengthHeader bool
	_                   [3]byte
	NoTrace             AgentConnTraceStateT
	Ssl                 bool
	_                   [3]byte
}

type AgentConnTraceStateT uint32

const (
	AgentConnTraceStateTUnset              AgentConnTraceStateT = 0
	AgentConnTraceStateTTraceable          AgentConnTraceStateT = 1
	AgentConnTraceStateTProtocolNotMatched AgentConnTraceStateT = 2
	AgentConnTraceStateTProtocolUnknown    AgentConnTraceStateT = 3
	AgentConnTraceStateTOther              AgentConnTraceStateT = 4
)

type AgentConnTypeT uint32

const (
	AgentConnTypeTKConnect       AgentConnTypeT = 0
	AgentConnTypeTKClose         AgentConnTypeT = 1
	AgentConnTypeTKProtocolInfer AgentConnTypeT = 2
)

type AgentControlValueIndexT uint32

const (
	AgentControlValueIndexTKTargetTGIDIndex          AgentControlValueIndexT = 0
	AgentControlValueIndexTKStirlingTGIDIndex        AgentControlValueIndexT = 1
	AgentControlValueIndexTKEnabledXdpIndex          AgentControlValueIndexT = 2
	AgentControlValueIndexTKEnableFilterByPid        AgentControlValueIndexT = 3
	AgentControlValueIndexTKEnableFilterByLocalPort  AgentControlValueIndexT = 4
	AgentControlValueIndexTKEnableFilterByRemotePort AgentControlValueIndexT = 5
	AgentControlValueIndexTKEnableFilterByRemoteHost AgentControlValueIndexT = 6
	AgentControlValueIndexTKSideFilter               AgentControlValueIndexT = 7
	AgentControlValueIndexTKNumControlValues         AgentControlValueIndexT = 8
	AgentControlValueIndexTKTraceProtocol            AgentControlValueIndexT = 9
)

type AgentEndpointRoleT uint32

const (
	AgentEndpointRoleTKRoleClient  AgentEndpointRoleT = 1
	AgentEndpointRoleTKRoleServer  AgentEndpointRoleT = 2
	AgentEndpointRoleTKRoleUnknown AgentEndpointRoleT = 4
)

type AgentFirstPacketEvt struct {
	Ts      uint64
	Len     uint32
	Flags   uint8
	_       [3]byte
	Ifindex uint32
	Step    AgentStepT
	Key     AgentSockKey
}

type AgentIn6Addr struct{ In6U struct{ U6Addr8 [16]uint8 } }

type AgentKernEvt struct {
	FuncName            [16]int8
	Ts                  uint64
	TsDelta             uint32
	Seq                 uint32
	Len                 uint32
	Flags               uint8
	PrependLengthHeader bool
	_                   [2]byte
	Ifindex             uint32
	_                   [4]byte
	ConnIdS             AgentConnIdS_t
	Step                AgentStepT
	LengthHeader        uint32
}

type AgentKernEvtData struct {
	Ke      AgentKernEvt
	BufSize uint32
	Msg     [30720]int8
	_       [4]byte
}

type AgentKernEvtSslData struct {
	Ke         AgentKernEvt
	SyscallSeq uint32
	SyscallLen uint32
	BufSize    uint32
	Msg        [30720]int8
	_          [4]byte
}

type AgentProcessExecEvent struct{ Pid int32 }

type AgentProcessExitEvent struct{ Pid int32 }

type AgentSockKey struct {
	Sip   [2]uint64
	Dip   [2]uint64
	Sport uint16
	Dport uint16
	_     [4]byte
}

type AgentSourceFunctionT uint32

const (
	AgentSourceFunctionTKSourceFunctionUnknown AgentSourceFunctionT = 0
	AgentSourceFunctionTKSyscallAccept         AgentSourceFunctionT = 1
	AgentSourceFunctionTKSyscallConnect        AgentSourceFunctionT = 2
	AgentSourceFunctionTKSyscallClose          AgentSourceFunctionT = 3
	AgentSourceFunctionTKSyscallWrite          AgentSourceFunctionT = 4
	AgentSourceFunctionTKSyscallRead           AgentSourceFunctionT = 5
	AgentSourceFunctionTKSyscallSend           AgentSourceFunctionT = 6
	AgentSourceFunctionTKSyscallRecv           AgentSourceFunctionT = 7
	AgentSourceFunctionTKSyscallSendTo         AgentSourceFunctionT = 8
	AgentSourceFunctionTKSyscallRecvFrom       AgentSourceFunctionT = 9
	AgentSourceFunctionTKSyscallSendMsg        AgentSourceFunctionT = 10
	AgentSourceFunctionTKSyscallRecvMsg        AgentSourceFunctionT = 11
	AgentSourceFunctionTKSyscallSendMMsg       AgentSourceFunctionT = 12
	AgentSourceFunctionTKSyscallRecvMMsg       AgentSourceFunctionT = 13
	AgentSourceFunctionTKSyscallWriteV         AgentSourceFunctionT = 14
	AgentSourceFunctionTKSyscallReadV          AgentSourceFunctionT = 15
	AgentSourceFunctionTKSyscallSendfile       AgentSourceFunctionT = 16
	AgentSourceFunctionTKGoTLSConnWrite        AgentSourceFunctionT = 17
	AgentSourceFunctionTKGoTLSConnRead         AgentSourceFunctionT = 18
	AgentSourceFunctionTKSSLWrite              AgentSourceFunctionT = 19
	AgentSourceFunctionTKSSLRead               AgentSourceFunctionT = 20
)

type AgentStepT uint32

const (
	AgentStepTStart       AgentStepT = 0
	AgentStepTSSL_OUT     AgentStepT = 1
	AgentStepTSYSCALL_OUT AgentStepT = 2
	AgentStepTTCP_OUT     AgentStepT = 3
	AgentStepTIP_OUT      AgentStepT = 4
	AgentStepTQDISC_OUT   AgentStepT = 5
	AgentStepTDEV_OUT     AgentStepT = 6
	AgentStepTNIC_OUT     AgentStepT = 7
	AgentStepTNIC_IN      AgentStepT = 8
	AgentStepTDEV_IN      AgentStepT = 9
	AgentStepTIP_IN       AgentStepT = 10
	AgentStepTTCP_IN      AgentStepT = 11
	AgentStepTUSER_COPY   AgentStepT = 12
	AgentStepTSYSCALL_IN  AgentStepT = 13
	AgentStepTSSL_IN      AgentStepT = 14
	AgentStepTEnd         AgentStepT = 15
)

type AgentTrafficDirectionT uint32

const (
	AgentTrafficDirectionTKEgress  AgentTrafficDirectionT = 0
	AgentTrafficDirectionTKIngress AgentTrafficDirectionT = 1
)

type AgentTrafficProtocolT uint32

const (
	AgentTrafficProtocolTKProtocolUnset    AgentTrafficProtocolT = 0
	AgentTrafficProtocolTKProtocolUnknown  AgentTrafficProtocolT = 1
	AgentTrafficProtocolTKProtocolHTTP     AgentTrafficProtocolT = 2
	AgentTrafficProtocolTKProtocolHTTP2    AgentTrafficProtocolT = 3
	AgentTrafficProtocolTKProtocolMySQL    AgentTrafficProtocolT = 4
	AgentTrafficProtocolTKProtocolCQL      AgentTrafficProtocolT = 5
	AgentTrafficProtocolTKProtocolPGSQL    AgentTrafficProtocolT = 6
	AgentTrafficProtocolTKProtocolDNS      AgentTrafficProtocolT = 7
	AgentTrafficProtocolTKProtocolRedis    AgentTrafficProtocolT = 8
	AgentTrafficProtocolTKProtocolNATS     AgentTrafficProtocolT = 9
	AgentTrafficProtocolTKProtocolMongo    AgentTrafficProtocolT = 10
	AgentTrafficProtocolTKProtocolKafka    AgentTrafficProtocolT = 11
	AgentTrafficProtocolTKProtocolMux      AgentTrafficProtocolT = 12
	AgentTrafficProtocolTKProtocolAMQP     AgentTrafficProtocolT = 13
	AgentTrafficProtocolTKProtocolRocketMQ AgentTrafficProtocolT = 14
	AgentTrafficProtocolTKNumProtocols     AgentTrafficProtocolT = 15
)

// LoadAgent returns the embedded CollectionSpec for Agent.
func LoadAgent() (*ebpf.CollectionSpec, error) {
	reader := bytes.NewReader(_AgentBytes)
	spec, err := ebpf.LoadCollectionSpecFromReader(reader)
	if err != nil {
		return nil, fmt.Errorf("can't load Agent: %w", err)
	}

	return spec, err
}

// LoadAgentObjects loads Agent and converts it into a struct.
//
// The following types are suitable as obj argument:
//
//	*AgentObjects
//	*AgentPrograms
//	*AgentMaps
//
// See ebpf.CollectionSpec.LoadAndAssign documentation for details.
func LoadAgentObjects(obj interface{}, opts *ebpf.CollectionOptions) error {
	spec, err := LoadAgent()
	if err != nil {
		return err
	}

	return spec.LoadAndAssign(obj, opts)
}

// AgentSpecs contains maps and programs before they are loaded into the kernel.
//
// It can be passed ebpf.CollectionSpec.Assign.
type AgentSpecs struct {
	AgentProgramSpecs
	AgentMapSpecs
}

// AgentSpecs contains programs before they are loaded into the kernel.
//
// It can be passed ebpf.CollectionSpec.Assign.
type AgentProgramSpecs struct {
	DevHardStartXmit                     *ebpf.ProgramSpec `ebpf:"dev_hard_start_xmit"`
	DevQueueXmit                         *ebpf.ProgramSpec `ebpf:"dev_queue_xmit"`
	FentrySecuritySocketRecvmsg          *ebpf.ProgramSpec `ebpf:"fentry__security_socket_recvmsg"`
	FentrySecuritySocketSendmsg          *ebpf.ProgramSpec `ebpf:"fentry__security_socket_sendmsg"`
	FentrySysAccept4                     *ebpf.ProgramSpec `ebpf:"fentry__sys_accept4"`
	FentrySysClose                       *ebpf.ProgramSpec `ebpf:"fentry__sys_close"`
	FentrySysConnect                     *ebpf.ProgramSpec `ebpf:"fentry__sys_connect"`
	FentrySysRead                        *ebpf.ProgramSpec `ebpf:"fentry__sys_read"`
	FentrySysReadv                       *ebpf.ProgramSpec `ebpf:"fentry__sys_readv"`
	FentrySysRecvfrom                    *ebpf.ProgramSpec `ebpf:"fentry__sys_recvfrom"`
	FentrySysRecvmmsg                    *ebpf.ProgramSpec `ebpf:"fentry__sys_recvmmsg"`
	FentrySysRecvmsg                     *ebpf.ProgramSpec `ebpf:"fentry__sys_recvmsg"`
	FentrySysSendfile64                  *ebpf.ProgramSpec `ebpf:"fentry__sys_sendfile64"`
	FentrySysSendmmsg                    *ebpf.ProgramSpec `ebpf:"fentry__sys_sendmmsg"`
	FentrySysSendmsg                     *ebpf.ProgramSpec `ebpf:"fentry__sys_sendmsg"`
	FentrySysSendto                      *ebpf.ProgramSpec `ebpf:"fentry__sys_sendto"`
	FentrySysWrite                       *ebpf.ProgramSpec `ebpf:"fentry__sys_write"`
	FentrySysWritev                      *ebpf.ProgramSpec `ebpf:"fentry__sys_writev"`
	FexitSysAccept4                      *ebpf.ProgramSpec `ebpf:"fexit__sys_accept4"`
	FexitSysClose                        *ebpf.ProgramSpec `ebpf:"fexit__sys_close"`
	FexitSysConnect                      *ebpf.ProgramSpec `ebpf:"fexit__sys_connect"`
	FexitSysRead                         *ebpf.ProgramSpec `ebpf:"fexit__sys_read"`
	FexitSysReadv                        *ebpf.ProgramSpec `ebpf:"fexit__sys_readv"`
	FexitSysRecvfrom                     *ebpf.ProgramSpec `ebpf:"fexit__sys_recvfrom"`
	FexitSysRecvmmsg                     *ebpf.ProgramSpec `ebpf:"fexit__sys_recvmmsg"`
	FexitSysRecvmsg                      *ebpf.ProgramSpec `ebpf:"fexit__sys_recvmsg"`
	FexitSysSendfile64                   *ebpf.ProgramSpec `ebpf:"fexit__sys_sendfile64"`
	FexitSysSendmmsg                     *ebpf.ProgramSpec `ebpf:"fexit__sys_sendmmsg"`
	FexitSysSendmsg                      *ebpf.ProgramSpec `ebpf:"fexit__sys_sendmsg"`
	FexitSysSendto                       *ebpf.ProgramSpec `ebpf:"fexit__sys_sendto"`
	FexitSysWrite                        *ebpf.ProgramSpec `ebpf:"fexit__sys_write"`
	FexitSysWritev                       *ebpf.ProgramSpec `ebpf:"fexit__sys_writev"`
	IpQueueXmit                          *ebpf.ProgramSpec `ebpf:"ip_queue_xmit"`
	IpQueueXmit2                         *ebpf.ProgramSpec `ebpf:"ip_queue_xmit2"`
	IpRcvCore                            *ebpf.ProgramSpec `ebpf:"ip_rcv_core"`
	KprobeNfNatManipPkt                  *ebpf.ProgramSpec `ebpf:"kprobe__nf_nat_manip_pkt"`
	KprobeNfNatPacket                    *ebpf.ProgramSpec `ebpf:"kprobe__nf_nat_packet"`
	SecuritySocketRecvmsgEnter           *ebpf.ProgramSpec `ebpf:"security_socket_recvmsg_enter"`
	SecuritySocketSendmsgEnter           *ebpf.ProgramSpec `ebpf:"security_socket_sendmsg_enter"`
	SkbCopyDatagramIovec                 *ebpf.ProgramSpec `ebpf:"skb_copy_datagram_iovec"`
	SkbCopyDatagramIter                  *ebpf.ProgramSpec `ebpf:"skb_copy_datagram_iter"`
	SockAllocRet                         *ebpf.ProgramSpec `ebpf:"sock_alloc_ret"`
	TcpDestroySock                       *ebpf.ProgramSpec `ebpf:"tcp_destroy_sock"`
	TcpQueueRcv                          *ebpf.ProgramSpec `ebpf:"tcp_queue_rcv"`
	TcpRcvEstablished                    *ebpf.ProgramSpec `ebpf:"tcp_rcv_established"`
	TcpV4DoRcv                           *ebpf.ProgramSpec `ebpf:"tcp_v4_do_rcv"`
	TcpV4Rcv                             *ebpf.ProgramSpec `ebpf:"tcp_v4_rcv"`
	TcpV6DoRcv                           *ebpf.ProgramSpec `ebpf:"tcp_v6_do_rcv"`
	TracepointNetifReceiveSkb            *ebpf.ProgramSpec `ebpf:"tracepoint__netif_receive_skb"`
	TracepointSchedSchedProcessExec      *ebpf.ProgramSpec `ebpf:"tracepoint__sched__sched_process_exec"`
	TracepointSchedSchedProcessExit      *ebpf.ProgramSpec `ebpf:"tracepoint__sched__sched_process_exit"`
	TracepointSkbCopyDatagramIovec       *ebpf.ProgramSpec `ebpf:"tracepoint__skb_copy_datagram_iovec"`
	TracepointSyscallsSysEnterAccept4    *ebpf.ProgramSpec `ebpf:"tracepoint__syscalls__sys_enter_accept4"`
	TracepointSyscallsSysEnterClose      *ebpf.ProgramSpec `ebpf:"tracepoint__syscalls__sys_enter_close"`
	TracepointSyscallsSysEnterConnect    *ebpf.ProgramSpec `ebpf:"tracepoint__syscalls__sys_enter_connect"`
	TracepointSyscallsSysEnterRead       *ebpf.ProgramSpec `ebpf:"tracepoint__syscalls__sys_enter_read"`
	TracepointSyscallsSysEnterReadv      *ebpf.ProgramSpec `ebpf:"tracepoint__syscalls__sys_enter_readv"`
	TracepointSyscallsSysEnterRecvfrom   *ebpf.ProgramSpec `ebpf:"tracepoint__syscalls__sys_enter_recvfrom"`
	TracepointSyscallsSysEnterRecvmmsg   *ebpf.ProgramSpec `ebpf:"tracepoint__syscalls__sys_enter_recvmmsg"`
	TracepointSyscallsSysEnterRecvmsg    *ebpf.ProgramSpec `ebpf:"tracepoint__syscalls__sys_enter_recvmsg"`
	TracepointSyscallsSysEnterSendfile64 *ebpf.ProgramSpec `ebpf:"tracepoint__syscalls__sys_enter_sendfile64"`
	TracepointSyscallsSysEnterSendmmsg   *ebpf.ProgramSpec `ebpf:"tracepoint__syscalls__sys_enter_sendmmsg"`
	TracepointSyscallsSysEnterSendmsg    *ebpf.ProgramSpec `ebpf:"tracepoint__syscalls__sys_enter_sendmsg"`
	TracepointSyscallsSysEnterSendto     *ebpf.ProgramSpec `ebpf:"tracepoint__syscalls__sys_enter_sendto"`
	TracepointSyscallsSysEnterWrite      *ebpf.ProgramSpec `ebpf:"tracepoint__syscalls__sys_enter_write"`
	TracepointSyscallsSysEnterWritev     *ebpf.ProgramSpec `ebpf:"tracepoint__syscalls__sys_enter_writev"`
	TracepointSyscallsSysExitAccept4     *ebpf.ProgramSpec `ebpf:"tracepoint__syscalls__sys_exit_accept4"`
	TracepointSyscallsSysExitClose       *ebpf.ProgramSpec `ebpf:"tracepoint__syscalls__sys_exit_close"`
	TracepointSyscallsSysExitConnect     *ebpf.ProgramSpec `ebpf:"tracepoint__syscalls__sys_exit_connect"`
	TracepointSyscallsSysExitRead        *ebpf.ProgramSpec `ebpf:"tracepoint__syscalls__sys_exit_read"`
	TracepointSyscallsSysExitReadv       *ebpf.ProgramSpec `ebpf:"tracepoint__syscalls__sys_exit_readv"`
	TracepointSyscallsSysExitRecvfrom    *ebpf.ProgramSpec `ebpf:"tracepoint__syscalls__sys_exit_recvfrom"`
	TracepointSyscallsSysExitRecvmmsg    *ebpf.ProgramSpec `ebpf:"tracepoint__syscalls__sys_exit_recvmmsg"`
	TracepointSyscallsSysExitRecvmsg     *ebpf.ProgramSpec `ebpf:"tracepoint__syscalls__sys_exit_recvmsg"`
	TracepointSyscallsSysExitSendfile64  *ebpf.ProgramSpec `ebpf:"tracepoint__syscalls__sys_exit_sendfile64"`
	TracepointSyscallsSysExitSendmmsg    *ebpf.ProgramSpec `ebpf:"tracepoint__syscalls__sys_exit_sendmmsg"`
	TracepointSyscallsSysExitSendmsg     *ebpf.ProgramSpec `ebpf:"tracepoint__syscalls__sys_exit_sendmsg"`
	TracepointSyscallsSysExitSendto      *ebpf.ProgramSpec `ebpf:"tracepoint__syscalls__sys_exit_sendto"`
	TracepointSyscallsSysExitWrite       *ebpf.ProgramSpec `ebpf:"tracepoint__syscalls__sys_exit_write"`
	TracepointSyscallsSysExitWritev      *ebpf.ProgramSpec `ebpf:"tracepoint__syscalls__sys_exit_writev"`
	XdpProxy                             *ebpf.ProgramSpec `ebpf:"xdp_proxy"`
}

// AgentMapSpecs contains maps before they are loaded into the kernel.
//
// It can be passed ebpf.CollectionSpec.Assign.
type AgentMapSpecs struct {
	AcceptArgsMap         *ebpf.MapSpec `ebpf:"accept_args_map"`
	ActiveSendfileArgsMap *ebpf.MapSpec `ebpf:"active_sendfile_args_map"`
	ActiveSslReadArgsMap  *ebpf.MapSpec `ebpf:"active_ssl_read_args_map"`
	ActiveSslWriteArgsMap *ebpf.MapSpec `ebpf:"active_ssl_write_args_map"`
	CloseArgsMap          *ebpf.MapSpec `ebpf:"close_args_map"`
	ConnEvtMap            *ebpf.MapSpec `ebpf:"conn_evt_map"`
	ConnEvtRb             *ebpf.MapSpec `ebpf:"conn_evt_rb"`
	ConnInfoMap           *ebpf.MapSpec `ebpf:"conn_info_map"`
	ConnInfoT_map         *ebpf.MapSpec `ebpf:"conn_info_t_map"`
	ConnectArgsMap        *ebpf.MapSpec `ebpf:"connect_args_map"`
	ControlValues         *ebpf.MapSpec `ebpf:"control_values"`
	EnabledLocalIpv4Map   *ebpf.MapSpec `ebpf:"enabled_local_ipv4_map"`
	EnabledLocalPortMap   *ebpf.MapSpec `ebpf:"enabled_local_port_map"`
	EnabledRemoteIpMap    *ebpf.MapSpec `ebpf:"enabled_remote_ip_map"`
	EnabledRemotePortMap  *ebpf.MapSpec `ebpf:"enabled_remote_port_map"`
	FilterMntnsMap        *ebpf.MapSpec `ebpf:"filter_mntns_map"`
	FilterNetnsMap        *ebpf.MapSpec `ebpf:"filter_netns_map"`
	FilterPidMap          *ebpf.MapSpec `ebpf:"filter_pid_map"`
	FilterPidnsMap        *ebpf.MapSpec `ebpf:"filter_pidns_map"`
	FirstPacketEvtMap     *ebpf.MapSpec `ebpf:"first_packet_evt_map"`
	FirstPacketRb         *ebpf.MapSpec `ebpf:"first_packet_rb"`
	GoCommonSymaddrsMap   *ebpf.MapSpec `ebpf:"go_common_symaddrs_map"`
	GoSslUserSpaceCallMap *ebpf.MapSpec `ebpf:"go_ssl_user_space_call_map"`
	KernEvtT_map          *ebpf.MapSpec `ebpf:"kern_evt_t_map"`
	NatFlowMap            *ebpf.MapSpec `ebpf:"nat_flow_map"`
	ProcExecEvents        *ebpf.MapSpec `ebpf:"proc_exec_events"`
	ProcExitEvents        *ebpf.MapSpec `ebpf:"proc_exit_events"`
	Rb                    *ebpf.MapSpec `ebpf:"rb"`
	ReadArgsMap           *ebpf.MapSpec `ebpf:"read_args_map"`
	SockKeyConnIdMap      *ebpf.MapSpec `ebpf:"sock_key_conn_id_map"`
	SockXmitMap           *ebpf.MapSpec `ebpf:"sock_xmit_map"`
	SslDataMap            *ebpf.MapSpec `ebpf:"ssl_data_map"`
	SslRb                 *ebpf.MapSpec `ebpf:"ssl_rb"`
	SslUserSpaceCallMap   *ebpf.MapSpec `ebpf:"ssl_user_space_call_map"`
	SyscallDataMap        *ebpf.MapSpec `ebpf:"syscall_data_map"`
	SyscallRb             *ebpf.MapSpec `ebpf:"syscall_rb"`
	WriteArgsMap          *ebpf.MapSpec `ebpf:"write_args_map"`
}

// AgentObjects contains all objects after they have been loaded into the kernel.
//
// It can be passed to LoadAgentObjects or ebpf.CollectionSpec.LoadAndAssign.
type AgentObjects struct {
	AgentPrograms
	AgentMaps
}

func (o *AgentObjects) Close() error {
	return _AgentClose(
		&o.AgentPrograms,
		&o.AgentMaps,
	)
}

// AgentMaps contains all maps after they have been loaded into the kernel.
//
// It can be passed to LoadAgentObjects or ebpf.CollectionSpec.LoadAndAssign.
type AgentMaps struct {
	AcceptArgsMap         *ebpf.Map `ebpf:"accept_args_map"`
	ActiveSendfileArgsMap *ebpf.Map `ebpf:"active_sendfile_args_map"`
	ActiveSslReadArgsMap  *ebpf.Map `ebpf:"active_ssl_read_args_map"`
	ActiveSslWriteArgsMap *ebpf.Map `ebpf:"active_ssl_write_args_map"`
	CloseArgsMap          *ebpf.Map `ebpf:"close_args_map"`
	ConnEvtMap            *ebpf.Map `ebpf:"conn_evt_map"`
	ConnEvtRb             *ebpf.Map `ebpf:"conn_evt_rb"`
	ConnInfoMap           *ebpf.Map `ebpf:"conn_info_map"`
	ConnInfoT_map         *ebpf.Map `ebpf:"conn_info_t_map"`
	ConnectArgsMap        *ebpf.Map `ebpf:"connect_args_map"`
	ControlValues         *ebpf.Map `ebpf:"control_values"`
	EnabledLocalIpv4Map   *ebpf.Map `ebpf:"enabled_local_ipv4_map"`
	EnabledLocalPortMap   *ebpf.Map `ebpf:"enabled_local_port_map"`
	EnabledRemoteIpMap    *ebpf.Map `ebpf:"enabled_remote_ip_map"`
	EnabledRemotePortMap  *ebpf.Map `ebpf:"enabled_remote_port_map"`
	FilterMntnsMap        *ebpf.Map `ebpf:"filter_mntns_map"`
	FilterNetnsMap        *ebpf.Map `ebpf:"filter_netns_map"`
	FilterPidMap          *ebpf.Map `ebpf:"filter_pid_map"`
	FilterPidnsMap        *ebpf.Map `ebpf:"filter_pidns_map"`
	FirstPacketEvtMap     *ebpf.Map `ebpf:"first_packet_evt_map"`
	FirstPacketRb         *ebpf.Map `ebpf:"first_packet_rb"`
	GoCommonSymaddrsMap   *ebpf.Map `ebpf:"go_common_symaddrs_map"`
	GoSslUserSpaceCallMap *ebpf.Map `ebpf:"go_ssl_user_space_call_map"`
	KernEvtT_map          *ebpf.Map `ebpf:"kern_evt_t_map"`
	NatFlowMap            *ebpf.Map `ebpf:"nat_flow_map"`
	ProcExecEvents        *ebpf.Map `ebpf:"proc_exec_events"`
	ProcExitEvents        *ebpf.Map `ebpf:"proc_exit_events"`
	Rb                    *ebpf.Map `ebpf:"rb"`
	ReadArgsMap           *ebpf.Map `ebpf:"read_args_map"`
	SockKeyConnIdMap      *ebpf.Map `ebpf:"sock_key_conn_id_map"`
	SockXmitMap           *ebpf.Map `ebpf:"sock_xmit_map"`
	SslDataMap            *ebpf.Map `ebpf:"ssl_data_map"`
	SslRb                 *ebpf.Map `ebpf:"ssl_rb"`
	SslUserSpaceCallMap   *ebpf.Map `ebpf:"ssl_user_space_call_map"`
	SyscallDataMap        *ebpf.Map `ebpf:"syscall_data_map"`
	SyscallRb             *ebpf.Map `ebpf:"syscall_rb"`
	WriteArgsMap          *ebpf.Map `ebpf:"write_args_map"`
}

func (m *AgentMaps) Close() error {
	return _AgentClose(
		m.AcceptArgsMap,
		m.ActiveSendfileArgsMap,
		m.ActiveSslReadArgsMap,
		m.ActiveSslWriteArgsMap,
		m.CloseArgsMap,
		m.ConnEvtMap,
		m.ConnEvtRb,
		m.ConnInfoMap,
		m.ConnInfoT_map,
		m.ConnectArgsMap,
		m.ControlValues,
		m.EnabledLocalIpv4Map,
		m.EnabledLocalPortMap,
		m.EnabledRemoteIpMap,
		m.EnabledRemotePortMap,
		m.FilterMntnsMap,
		m.FilterNetnsMap,
		m.FilterPidMap,
		m.FilterPidnsMap,
		m.FirstPacketEvtMap,
		m.FirstPacketRb,
		m.GoCommonSymaddrsMap,
		m.GoSslUserSpaceCallMap,
		m.KernEvtT_map,
		m.NatFlowMap,
		m.ProcExecEvents,
		m.ProcExitEvents,
		m.Rb,
		m.ReadArgsMap,
		m.SockKeyConnIdMap,
		m.SockXmitMap,
		m.SslDataMap,
		m.SslRb,
		m.SslUserSpaceCallMap,
		m.SyscallDataMap,
		m.SyscallRb,
		m.WriteArgsMap,
	)
}

// AgentPrograms contains all programs after they have been loaded into the kernel.
//
// It can be passed to LoadAgentObjects or ebpf.CollectionSpec.LoadAndAssign.
type AgentPrograms struct {
	DevHardStartXmit                     *ebpf.Program `ebpf:"dev_hard_start_xmit"`
	DevQueueXmit                         *ebpf.Program `ebpf:"dev_queue_xmit"`
	FentrySecuritySocketRecvmsg          *ebpf.Program `ebpf:"fentry__security_socket_recvmsg"`
	FentrySecuritySocketSendmsg          *ebpf.Program `ebpf:"fentry__security_socket_sendmsg"`
	FentrySysAccept4                     *ebpf.Program `ebpf:"fentry__sys_accept4"`
	FentrySysClose                       *ebpf.Program `ebpf:"fentry__sys_close"`
	FentrySysConnect                     *ebpf.Program `ebpf:"fentry__sys_connect"`
	FentrySysRead                        *ebpf.Program `ebpf:"fentry__sys_read"`
	FentrySysReadv                       *ebpf.Program `ebpf:"fentry__sys_readv"`
	FentrySysRecvfrom                    *ebpf.Program `ebpf:"fentry__sys_recvfrom"`
	FentrySysRecvmmsg                    *ebpf.Program `ebpf:"fentry__sys_recvmmsg"`
	FentrySysRecvmsg                     *ebpf.Program `ebpf:"fentry__sys_recvmsg"`
	FentrySysSendfile64                  *ebpf.Program `ebpf:"fentry__sys_sendfile64"`
	FentrySysSendmmsg                    *ebpf.Program `ebpf:"fentry__sys_sendmmsg"`
	FentrySysSendmsg                     *ebpf.Program `ebpf:"fentry__sys_sendmsg"`
	FentrySysSendto                      *ebpf.Program `ebpf:"fentry__sys_sendto"`
	FentrySysWrite                       *ebpf.Program `ebpf:"fentry__sys_write"`
	FentrySysWritev                      *ebpf.Program `ebpf:"fentry__sys_writev"`
	FexitSysAccept4                      *ebpf.Program `ebpf:"fexit__sys_accept4"`
	FexitSysClose                        *ebpf.Program `ebpf:"fexit__sys_close"`
	FexitSysConnect                      *ebpf.Program `ebpf:"fexit__sys_connect"`
	FexitSysRead                         *ebpf.Program `ebpf:"fexit__sys_read"`
	FexitSysReadv                        *ebpf.Program `ebpf:"fexit__sys_readv"`
	FexitSysRecvfrom                     *ebpf.Program `ebpf:"fexit__sys_recvfrom"`
	FexitSysRecvmmsg                     *ebpf.Program `ebpf:"fexit__sys_recvmmsg"`
	FexitSysRecvmsg                      *ebpf.Program `ebpf:"fexit__sys_recvmsg"`
	FexitSysSendfile64                   *ebpf.Program `ebpf:"fexit__sys_sendfile64"`
	FexitSysSendmmsg                     *ebpf.Program `ebpf:"fexit__sys_sendmmsg"`
	FexitSysSendmsg                      *ebpf.Program `ebpf:"fexit__sys_sendmsg"`
	FexitSysSendto                       *ebpf.Program `ebpf:"fexit__sys_sendto"`
	FexitSysWrite                        *ebpf.Program `ebpf:"fexit__sys_write"`
	FexitSysWritev                       *ebpf.Program `ebpf:"fexit__sys_writev"`
	IpQueueXmit                          *ebpf.Program `ebpf:"ip_queue_xmit"`
	IpQueueXmit2                         *ebpf.Program `ebpf:"ip_queue_xmit2"`
	IpRcvCore                            *ebpf.Program `ebpf:"ip_rcv_core"`
	KprobeNfNatManipPkt                  *ebpf.Program `ebpf:"kprobe__nf_nat_manip_pkt"`
	KprobeNfNatPacket                    *ebpf.Program `ebpf:"kprobe__nf_nat_packet"`
	SecuritySocketRecvmsgEnter           *ebpf.Program `ebpf:"security_socket_recvmsg_enter"`
	SecuritySocketSendmsgEnter           *ebpf.Program `ebpf:"security_socket_sendmsg_enter"`
	SkbCopyDatagramIovec                 *ebpf.Program `ebpf:"skb_copy_datagram_iovec"`
	SkbCopyDatagramIter                  *ebpf.Program `ebpf:"skb_copy_datagram_iter"`
	SockAllocRet                         *ebpf.Program `ebpf:"sock_alloc_ret"`
	TcpDestroySock                       *ebpf.Program `ebpf:"tcp_destroy_sock"`
	TcpQueueRcv                          *ebpf.Program `ebpf:"tcp_queue_rcv"`
	TcpRcvEstablished                    *ebpf.Program `ebpf:"tcp_rcv_established"`
	TcpV4DoRcv                           *ebpf.Program `ebpf:"tcp_v4_do_rcv"`
	TcpV4Rcv                             *ebpf.Program `ebpf:"tcp_v4_rcv"`
	TcpV6DoRcv                           *ebpf.Program `ebpf:"tcp_v6_do_rcv"`
	TracepointNetifReceiveSkb            *ebpf.Program `ebpf:"tracepoint__netif_receive_skb"`
	TracepointSchedSchedProcessExec      *ebpf.Program `ebpf:"tracepoint__sched__sched_process_exec"`
	TracepointSchedSchedProcessExit      *ebpf.Program `ebpf:"tracepoint__sched__sched_process_exit"`
	TracepointSkbCopyDatagramIovec       *ebpf.Program `ebpf:"tracepoint__skb_copy_datagram_iovec"`
	TracepointSyscallsSysEnterAccept4    *ebpf.Program `ebpf:"tracepoint__syscalls__sys_enter_accept4"`
	TracepointSyscallsSysEnterClose      *ebpf.Program `ebpf:"tracepoint__syscalls__sys_enter_close"`
	TracepointSyscallsSysEnterConnect    *ebpf.Program `ebpf:"tracepoint__syscalls__sys_enter_connect"`
	TracepointSyscallsSysEnterRead       *ebpf.Program `ebpf:"tracepoint__syscalls__sys_enter_read"`
	TracepointSyscallsSysEnterReadv      *ebpf.Program `ebpf:"tracepoint__syscalls__sys_enter_readv"`
	TracepointSyscallsSysEnterRecvfrom   *ebpf.Program `ebpf:"tracepoint__syscalls__sys_enter_recvfrom"`
	TracepointSyscallsSysEnterRecvmmsg   *ebpf.Program `ebpf:"tracepoint__syscalls__sys_enter_recvmmsg"`
	TracepointSyscallsSysEnterRecvmsg    *ebpf.Program `ebpf:"tracepoint__syscalls__sys_enter_recvmsg"`
	TracepointSyscallsSysEnterSendfile64 *ebpf.Program `ebpf:"tracepoint__syscalls__sys_enter_sendfile64"`
	TracepointSyscallsSysEnterSendmmsg   *ebpf.Program `ebpf:"tracepoint__syscalls__sys_enter_sendmmsg"`
	TracepointSyscallsSysEnterSendmsg    *ebpf.Program `ebpf:"tracepoint__syscalls__sys_enter_sendmsg"`
	TracepointSyscallsSysEnterSendto     *ebpf.Program `ebpf:"tracepoint__syscalls__sys_enter_sendto"`
	TracepointSyscallsSysEnterWrite      *ebpf.Program `ebpf:"tracepoint__syscalls__sys_enter_write"`
	TracepointSyscallsSysEnterWritev     *ebpf.Program `ebpf:"tracepoint__syscalls__sys_enter_writev"`
	TracepointSyscallsSysExitAccept4     *ebpf.Program `ebpf:"tracepoint__syscalls__sys_exit_accept4"`
	TracepointSyscallsSysExitClose       *ebpf.Program `ebpf:"tracepoint__syscalls__sys_exit_close"`
	TracepointSyscallsSysExitConnect     *ebpf.Program `ebpf:"tracepoint__syscalls__sys_exit_connect"`
	TracepointSyscallsSysExitRead        *ebpf.Program `ebpf:"tracepoint__syscalls__sys_exit_read"`
	TracepointSyscallsSysExitReadv       *ebpf.Program `ebpf:"tracepoint__syscalls__sys_exit_readv"`
	TracepointSyscallsSysExitRecvfrom    *ebpf.Program `ebpf:"tracepoint__syscalls__sys_exit_recvfrom"`
	TracepointSyscallsSysExitRecvmmsg    *ebpf.Program `ebpf:"tracepoint__syscalls__sys_exit_recvmmsg"`
	TracepointSyscallsSysExitRecvmsg     *ebpf.Program `ebpf:"tracepoint__syscalls__sys_exit_recvmsg"`
	TracepointSyscallsSysExitSendfile64  *ebpf.Program `ebpf:"tracepoint__syscalls__sys_exit_sendfile64"`
	TracepointSyscallsSysExitSendmmsg    *ebpf.Program `ebpf:"tracepoint__syscalls__sys_exit_sendmmsg"`
	TracepointSyscallsSysExitSendmsg     *ebpf.Program `ebpf:"tracepoint__syscalls__sys_exit_sendmsg"`
	TracepointSyscallsSysExitSendto      *ebpf.Program `ebpf:"tracepoint__syscalls__sys_exit_sendto"`
	TracepointSyscallsSysExitWrite       *ebpf.Program `ebpf:"tracepoint__syscalls__sys_exit_write"`
	TracepointSyscallsSysExitWritev      *ebpf.Program `ebpf:"tracepoint__syscalls__sys_exit_writev"`
	XdpProxy                             *ebpf.Program `ebpf:"xdp_proxy"`
}

func (p *AgentPrograms) Close() error {
	return _AgentClose(
		p.DevHardStartXmit,
		p.DevQueueXmit,
		p.FentrySecuritySocketRecvmsg,
		p.FentrySecuritySocketSendmsg,
		p.FentrySysAccept4,
		p.FentrySysClose,
		p.FentrySysConnect,
		p.FentrySysRead,
		p.FentrySysReadv,
		p.FentrySysRecvfrom,
		p.FentrySysRecvmmsg,
		p.FentrySysRecvmsg,
		p.FentrySysSendfile64,
		p.FentrySysSendmmsg,
		p.FentrySysSendmsg,
		p.FentrySysSendto,
		p.FentrySysWrite,
		p.FentrySysWritev,
		p.FexitSysAccept4,
		p.FexitSysClose,
		p.FexitSysConnect,
		p.FexitSysRead,
		p.FexitSysReadv,
		p.FexitSysRecvfrom,
		p.FexitSysRecvmmsg,
		p.FexitSysRecvmsg,
		p.FexitSysSendfile64,
		p.FexitSysSendmmsg,
		p.FexitSysSendmsg,
		p.FexitSysSendto,
		p.FexitSysWrite,
		p.FexitSysWritev,
		p.IpQueueXmit,
		p.IpQueueXmit2,
		p.IpRcvCore,
		p.KprobeNfNatManipPkt,
		p.KprobeNfNatPacket,
		p.SecuritySocketRecvmsgEnter,
		p.SecuritySocketSendmsgEnter,
		p.SkbCopyDatagramIovec,
		p.SkbCopyDatagramIter,
		p.SockAllocRet,
		p.TcpDestroySock,
		p.TcpQueueRcv,
		p.TcpRcvEstablished,
		p.TcpV4DoRcv,
		p.TcpV4Rcv,
		p.TcpV6DoRcv,
		p.TracepointNetifReceiveSkb,
		p.TracepointSchedSchedProcessExec,
		p.TracepointSchedSchedProcessExit,
		p.TracepointSkbCopyDatagramIovec,
		p.TracepointSyscallsSysEnterAccept4,
		p.TracepointSyscallsSysEnterClose,
		p.TracepointSyscallsSysEnterConnect,
		p.TracepointSyscallsSysEnterRead,
		p.TracepointSyscallsSysEnterReadv,
		p.TracepointSyscallsSysEnterRecvfrom,
		p.TracepointSyscallsSysEnterRecvmmsg,
		p.TracepointSyscallsSysEnterRecvmsg,
		p.TracepointSyscallsSysEnterSendfile64,
		p.TracepointSyscallsSysEnterSendmmsg,
		p.TracepointSyscallsSysEnterSendmsg,
		p.TracepointSyscallsSysEnterSendto,
		p.TracepointSyscallsSysEnterWrite,
		p.TracepointSyscallsSysEnterWritev,
		p.TracepointSyscallsSysExitAccept4,
		p.TracepointSyscallsSysExitClose,
		p.TracepointSyscallsSysExitConnect,
		p.TracepointSyscallsSysExitRead,
		p.TracepointSyscallsSysExitReadv,
		p.TracepointSyscallsSysExitRecvfrom,
		p.TracepointSyscallsSysExitRecvmmsg,
		p.TracepointSyscallsSysExitRecvmsg,
		p.TracepointSyscallsSysExitSendfile64,
		p.TracepointSyscallsSysExitSendmmsg,
		p.TracepointSyscallsSysExitSendmsg,
		p.TracepointSyscallsSysExitSendto,
		p.TracepointSyscallsSysExitWrite,
		p.TracepointSyscallsSysExitWritev,
		p.XdpProxy,
	)
}

func _AgentClose(closers ...io.Closer) error {
	for _, closer := range closers {
		if err := closer.Close(); err != nil {
			return err
		}
	}
	return nil
}

// Do not access this directly.
//
//go:embed agent_x86_bpfel.o
var _AgentBytes []byte
