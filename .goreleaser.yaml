# This is an example .goreleaser.yml file with some sensible defaults.
# Make sure to check the documentation at https://goreleaser.com

# The lines below are called `modelines`. See `:help modeline`
# Feel free to remove those if you don't want/need to use them.
# yaml-language-server: $schema=https://goreleaser.com/static/schema.json
# vim: set ts=2 sw=2 tw=0 fo=cnqoj

version: 2
project_name: kyanos
before:
  hooks:
    - wget -O - https://apt.llvm.org/llvm-snapshot.gpg.key|sudo apt-key add -
    - sudo add-apt-repository "deb http://apt.llvm.org/jammy/ llvm-toolchain-jammy-15 main"
    - sudo apt-key adv --keyserver keyserver.ubuntu.com --recv-keys 15CF4D18AF4F7421
    - sudo apt update
    - sudo apt-get install -y gcc flex bison make autoconf libelf-dev gcc-aarch64-linux-gnu libc6-dev-arm64-cross pkg-config llvm clang rsync upx
    - git submodule update --init --recursive

builds:
  - id: kyanos-arm64
    binary: kyanos
    env:
      - CGO_ENABLED=1
      - CC=aarch64-linux-gnu-gcc
    flags:
      - -tags=static
    ldflags:
      - -s
      - -w
      - -X "kyanos/version.Version={{.Version}}"
      - -X "kyanos/version.CommitID={{.Commit}}"
      - -X "kyanos/version.BuildTime={{.Date}}"
      - -linkmode 'external'
      - -extldflags "-static"
    goos:
      - linux
    goarch:
      - arm64
    hooks:
      pre:
        - bash -c 'sudo make clean || true'
        - bash -c 'sudo make build-bpf' 
        - bash -c 'sudo make btfgen BUILD_ARCH=arm64 ARCH_BPF_NAME=arm64' 


  - id: kyanos-amd64
    binary: kyanos
    env:
      - CGO_ENABLED=1
      - CC=gcc
      # - CGO_LDFLAGS="-Xlinker -rpath=. -static"
    flags:
      - -tags=static
    ldflags:
      - -s
      - -w
      - -X "kyanos/version.Version={{.Version}}"
      - -X "kyanos/version.CommitID={{.Commit}}"
      - -X "kyanos/version.BuildTime={{.Date}}"
      - -linkmode 'external'
      - -extldflags "-static"
    goos:
      - linux
    goarch:
      - amd64
    hooks:
      pre:
        - bash -c 'sudo make clean || true'
        - bash -c 'sudo make build-bpf'
        - bash -c 'sudo make btfgen BUILD_ARCH=x86_64 ARCH_BPF_NAME=x86' 

archives:
  - builds:
      - kyanos-arm64
      - kyanos-amd64

checksum:
  name_template: 'checksums.txt'
  
release:
  prerelease: auto
  
snapshot:
  version_template: "{{ .Tag }}-next"