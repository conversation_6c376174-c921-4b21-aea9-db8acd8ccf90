---
next:
  text: "Watch 使用方法"
  link: "./watch"
prev: false
---

# 5 分钟学会使用 kyanos

kyanos 共有三个子命令：watch、stat、overview。每个命令的作用如下：

1. watch：根据你指定的条件抓取网络流量并自动解析为请求响应记录。
2. stat：根据你指定的条件抓取请求响应记录并且聚合这些记录，得到更高维度的统计信息。
3. overview：一键展示当前机器依赖的外部资源。

## `watch` 流量采集简单用法

最简单的用法如下，抓取所有 kyanos 当前能够识别的协议

```bash
./kyanos watch
```

每个请求响应记录会记录在表格中的一行，每列记录这个请求的基本信息。你可以通过方向键或者 j/k 上下移动来选择记录：
![kyanos watch result](/watch-result.jpg)

按下 `enter` 进入详情界面：

![kyanos watch result detail](/watch-result-detail.jpg)

详情界面里第一部分是
**耗时详情**，每一个方块代表数据包经过的节点，比如这里有进程、网卡、Socket 缓冲区等。  
每个方块下面有一个耗时，这里的耗时指从上个节点到这个节点经过的时间。可以清楚的看到请求从进程发送到网卡，响应再从网卡复制到被进程读取的流程和每一个步骤的耗时。

第二部分是
**请求响应的基本信息**，包含请求响应的开始和结束时间，请求响应的大小等。

第三部分是
**请求响应的具体内容**，分为 Request 和 Response 两部分，超过 1024 字节会截断展示。

抓取流量时一般会更有针对性，比如抓取 HTTP 流量：

```bash
./kyanos watch http
```

更进一步，你可能只想抓取某个 HTTP Path 的流量：

```bash
./kyanos watch http --path /abc
```

每种协议都具有不同的过滤条件，而且 watch 还可以根据很多其他条件过滤，详情参考：[如何抓取请求响应和耗时细节](./watch)

## `stat` 聚合分析简单用法

在真实场景中，watch 输出的结果过于细粒度，因此 kyanos 提供了 stat 命令用于
**统计分析**。

简单来说 stat 可以用来回答这些问题：哪些连接的请求数最多？哪些远程服务端上的平均耗时最高？哪些客户端发送的请求带宽占比最高？

现在让我们来尝试找到哪些远程服务端上的平均耗时最高，我们只需指定 `--slow`
选项代表我们关心的指标是耗时，和 watch 一样，stat 也可以使用所有的过滤条件，这里我们只收集 PATH
=/abc 的 HTTP 请求 ：

```bash
./kyanos stat http --slow --path /abc
```

kyanos 默认会收集 10s（可以通过 `--time` 参数指定收集时间，当然，你也可以按下
`ctrl+c` 提前结束收集）： ![kyanos stat slow result](/qs-stat-slow.jpg)  
10s 结束后收集结果展示在表格里：

```js{6-8}
    Colleted events are here!

┌──────────────────────────────────────────────────────────────────────────────────────────────┐
│ id   remote-ip        max(ms)     avg(ms)     p50(ms)     p90(ms)     p99(ms)     count      │// [!code focus]
│──────────────────────────────────────────────────────────────────────────────────────────────│
│ 0    ***********      108.59      60.36       64.00       128.00      128.00      3          │// [!code focus]
│ 1    **************   11.56       11.56       16.00       16.00       16.00       1          │
│ 2    **************   11.98       11.51       13.33       16.00       16.00       3          │
│                                                                                              │
│                                                                                              │
│                                                                                              │
└──────────────────────────────────────────────────────────────────────────────────────────────┘
  ↑/k up • ↓/j down

  1 sort by name • 2 sort by max • 3 sort by avg • 4 sort by p50 • 5 sort by p90 • 6 sort by p99 • 7 sort by count • 8 sort by total
```

watch 展示的每一行是一次请求响应，而 stat 会按照某个维度聚合请求响应。

在这个例子中没有指定，默认采用 **请求的服务端地址（remote-ip）**
作为聚合维度（第二列就是聚合维度列），相同远程服务 IP 的请求响应结果会聚合起来（当然不止这一种聚合方式，了解更多请参考：[流量分析](./stat)）。

后面的 `max` 列就是这些相同 remote-ip 的请求响应中的最大耗时，`avg`
就是平均耗时等等。所以如果某个远程服务端出现了异常，你可以很快的通过比对不同 remote-ip 的指标发现异常的服务端是 ***********。

如果想查看这个 remote-ip 上的请求响应的详细信息，可以移动到这行记录上按下
`enter`，进入这个 remote-ip 上的请求响应列表中：

```js
 Events Num: 3

┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
│ id     Process               Connection                                Proto   TotalTime↓  ReqSize     RespSize    Net/Internal   ReadSocketTime  │// [! code focus]
│───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────│
│ 1      1315398<barad_agent>  10.0.4.9:38458 => ***********:80          HTTP    108.59      564         216         107.18         1.36            │// [! code focus]
│ 2      1315398<barad_agent>  10.0.4.9:38482 => ***********:80          HTTP    45.89       676         216         43.83          2.00            │// [! code focus]
│ 3      1315398<barad_agent>  10.0.4.9:38470 => ***********:80          HTTP    26.60       588         216         25.21          1.30            │
│                                                                                                                                                   │
└───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
  ↑/k up • ↓/j down
```

这里实际和 watch 命令展示的结果是相同的形式，每一行代表请求响应记录，之后你可以通过
`enter` 继续选择你感兴趣的一行记录，查看它的耗时细节和具体内容。

> [!TIP]
>
> stat 的功能十分强大，推荐你阅读：[如何聚合分析](./stat)
> 查看 stat 命令的其它用法。

## 下一步

了解每个命令的详细使用方法：

- watch 命令请查看：[如何抓取请求响应和耗时细节](./watch)
- stat 命令请查看：[如何聚合分析](./stat)
