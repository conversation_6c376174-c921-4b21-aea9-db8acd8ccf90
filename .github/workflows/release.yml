name: release

on:
  workflow_dispatch:
  push:
    tags:
      - v*

permissions:
  contents: read

jobs:

  release:
    runs-on: ubuntu-22.04
    timeout-minutes: 60
    steps:

      - name: Remove large directories
        run: |
          df -h
          cd /opt/hostedtoolcache
          find . -mindepth 1 -maxdepth 1 -type d -not -name 'go' -exec rm -rf {} +
          df -h
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4

      - name: Set up Go
        uses: actions/setup-go@3041bf56c941b39c61721a86cd11f3bb1338122a # v5
        with:
          go-version: '1.22.6'

      - name: Run GoReleaser
        uses: goreleaser/goreleaser-action@9ed2f89a662bf1735a48bc8557fd212fa902bebf # v6
        with:
          distribution: goreleaser
          version: latest
          args: release --clean --draft=true -p 1
        env:
          GITHUB_TOKEN: ${{ secrets.GORELEASE_TOKEN }}
