name: Test

on:
  workflow_dispatch:
  push:
    branches: [ "main", "dev","feature/*","unstable" ]
  pull_request:
    branches: [ "main", "dev","feature/*","unstable" ]

env:
  kyanos_log_option: --bpf-event-log-level 5 --conntrack-log-level 5 --agent-log-level 5

permissions:
  contents: read

jobs:

  build:
    runs-on: ubuntu-22.04
    timeout-minutes: 60
    steps:
      - uses: actions/checkout@v4.2.2
        with:
          submodules: recursive

      - name: Set up Go
        uses: actions/setup-go@v5.3.0
        with:
          go-version: '1.22.4'

      - name: Build
        run: |
          sudo apt update
          sudo apt install -y git
          sudo apt-get -y install pkg-config
          sudo apt install -y libelf-dev

          wget -O - https://apt.llvm.org/llvm-snapshot.gpg.key|sudo apt-key add -
          sudo add-apt-repository "deb http://apt.llvm.org/jammy/ llvm-toolchain-jammy-15 main"
          sudo apt update

          sudo apt install -y llvm
          sudo apt install -y clang
          pwd
          ls -l
          make clean && make build-bpf && make

      # - name: Test
      #   run: make test

      - name: Store executable
        uses: actions/upload-artifact@v4.6.1
        with:
          name: kyanos
          path: kyanos


  e2e-test:
    name: e2e-test
    needs: build
    strategy:
      fail-fast: false
      matrix:
        kernel:
          # renovate: datasource=docker depName=quay.io/lvh-images/kernel-images
          - '4.19-20240912.022020'
          # renovate: datasource=docker depName=quay.io/lvh-images/kernel-images
          - '5.4-20240912.022020'
          # renovate: datasource=docker depName=quay.io/lvh-images/kernel-images
          - '5.10-20240912.022020'
          # renovate: datasource=docker depName=quay.io/lvh-images/kernel-images
          - '5.15-20240912.022020'
          # renovate: datasource=docker depName=quay.io/lvh-images/kernel-images
          - '6.1-20240912.022020'
          # renovate: datasource=docker depName=quay.io/lvh-images/kernel-images
          - '6.6-20240912.022020'
          # renovate: datasource=docker depName=quay.io/lvh-images/kernel-images
          - 'bpf-20240912.022020'
          # renovate: datasource=docker depName=quay.io/lvh-images/kernel-images
          - 'bpf-next-20240912.022020'
    timeout-minutes: 30
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4.2.2
        with:
          submodules: recursive

      - name: Retrieve stored kyanos executable
        uses: actions/download-artifact@v4.1.9
        with:
          name: kyanos
          path: kyanos

      - name: Provision LVH VMs
        uses: cilium/little-vm-helper@c44c1221b104ee02ec0235211f7ace3c88eb11a2 # v0.0.19
        with:
          test-name: kyanos-test
          image-version: ${{ matrix.kernel }}
          cpu: 2
          mem: '4G'
          host-mount: ./
          install-dependencies: 'true'
          cmd: |
            chmod +x /host/kyanos/kyanos

      - name: download btf file
        if: ${{ startsWith(matrix.kernel, '4.') }}
        run: |
          img=quay.io/lvh-images/kernel-images:${{ matrix.kernel }} 
          docker pull $img
          id=$(docker create $img)
          mkdir data/
          docker cp $id:/data/kernels data/
          ls -la data/
          find data/ -path "*vmlinuz*" -type f
          find data/ -path "*btf*" -type f

      - name: copy btf file
        if: ${{ startsWith(matrix.kernel, '4.') }}
        uses: cilium/little-vm-helper@c44c1221b104ee02ec0235211f7ace3c88eb11a2 # v0.0.19
        with:
          provision: 'false'
          cmd: |
            uname -a
            cat /etc/issue
            cat /etc/os-release

            sudo mkdir -p /var/lib/kyanos/btf/

            sudo cp /host/data/kernels/4.*/boot/btf-4.* /var/lib/kyanos/btf/current.btf
            # btf_file=$(find /host/ -path "*btf*" -type f)
            # sudo cp $btf_file /var/lib/ptcpdump/btf/vmlinux

      - name: Install dependencies
        uses: cilium/little-vm-helper@c44c1221b104ee02ec0235211f7ace3c88eb11a2 # v0.0.19
        with:
          provision: 'false'
          cmd: |
            apt-get update
            apt-get install -y dnsutils || true
            apt-get install -y bind9-dnsutils || true
            # install btf
            if [ -f "/sys/kernel/btf/vmlinux" ]; then  
              mkdir -p /var/lib/kyanos/btf
              cp -f /sys/kernel/btf/vmlinux /var/lib/kyanos/btf/current.btf
            fi
            # install nerdctl
            wget https://github.com/containerd/nerdctl/releases/download/v1.7.6/nerdctl-1.7.6-linux-amd64.tar.gz
            sudo tar Cxzvvf /usr/local/bin nerdctl-1.7.6-linux-amd64.tar.gz
            wget https://github.com/containernetworking/plugins/releases/download/v1.5.0/cni-plugins-linux-amd64-v1.5.0.tgz
            sudo mkdir -p /opt/cni/bin
            sudo tar Cxzvvf /opt/cni/bin cni-plugins-linux-amd64-v1.5.0.tgz

            #install redis-cli
            sudo apt install -y redis-tools

            #install python pip
            sudo apt install -y python3 python3-pip pipx

            #install docker-compose
            # apt-get install -y docker-compose-plugin
            sudo curl -L "https://github.com/docker/compose/releases/download/v2.23.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
            sudo chmod +x /usr/local/bin/docker-compose
            docker-compose --version

            #install mongodb shell
            wget -qO- https://www.mongodb.org/static/pgp/server-8.0.asc | sudo tee /etc/apt/trusted.gpg.d/server-8.0.asc
            echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu jammy/mongodb-org/8.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-8.0.list
            sudo apt-get update
            sudo apt-get install -y mongodb-mongosh

      - name: Test DNS
        uses: cilium/little-vm-helper@c44c1221b104ee02ec0235211f7ace3c88eb11a2 # v0.0.19
        with:
          provision: 'false'
          cmd: |
            set -euxo pipefail
            uname -a
            cat /etc/issue
            pushd /host
            if [ -f "/var/lib/kyanos/btf/current.btf" ]; then  
                bash /host/testdata/test_dns.sh 'sudo /host/kyanos/kyanos $kyanos_log_option --btf /var/lib/kyanos/btf/current.btf'
            else  
                bash /host/testdata/test_dns.sh 'sudo /host/kyanos/kyanos $kyanos_log_option'
            fi
            popd
      - name: Test Mongo
        uses: cilium/little-vm-helper@c44c1221b104ee02ec0235211f7ace3c88eb11a2 # v0.0.19
        with:
          provision: 'false'
          cmd: |
            set -euxo pipefail
            uname -a
            cat /etc/issue
            pushd /host
            if [ -f "/var/lib/kyanos/btf/current.btf" ]; then  
                bash /host/testdata/test_mongodb.sh 'sudo /host/kyanos/kyanos $kyanos_log_option --btf /var/lib/kyanos/btf/current.btf'
            else  
                bash /host/testdata/test_mongodb.sh 'sudo /host/kyanos/kyanos $kyanos_log_option'
            fi
            popd

      - name: Test Truncated Data parsing
        uses: cilium/little-vm-helper@c44c1221b104ee02ec0235211f7ace3c88eb11a2 # v0.0.19
        with:
          provision: 'false'
          cmd: |
            set -euxo pipefail
            uname -a
            cat /etc/issue
            pushd /host
            if [ -f "/var/lib/kyanos/btf/current.btf" ]; then  
                bash /host/testdata/test_truncated_data.sh 'sudo /host/kyanos/kyanos $kyanos_log_option --btf /var/lib/kyanos/btf/current.btf'
            else  
                bash /host/testdata/test_truncated_data.sh 'sudo /host/kyanos/kyanos $kyanos_log_option'
            fi
            popd

      - name: Test Kafka
        uses: cilium/little-vm-helper@c44c1221b104ee02ec0235211f7ace3c88eb11a2 # v0.0.19
        with:
          provision: 'false'
          cmd: |
            set -euxo pipefail
            uname -a
            cat /etc/issue
            pushd /host
            if [ -f "/var/lib/kyanos/btf/current.btf" ]; then  
                bash /host/testdata/test_kafka.sh 'sudo /host/kyanos/kyanos $kyanos_log_option --btf /var/lib/kyanos/btf/current.btf'
            else  
                bash /host/testdata/test_kafka.sh 'sudo /host/kyanos/kyanos $kyanos_log_option'
            fi
            popd

      - name: Test CAP_BPF privilege check
        uses: cilium/little-vm-helper@c44c1221b104ee02ec0235211f7ace3c88eb11a2 # v0.0.19
        if: ${{ !contains(fromJSON('["4.19-20240912.022020", "5.4-20240912.022020"]'), matrix.kernel) }}
        with:
          provision: 'false'
          cmd: |
            set -euxo pipefail
            uname -a
            cat /etc/issue
            pushd /host
            bash /host/testdata/run_cap_bpf_test.sh "" "CAP_BPF"
            popd

      - name: Test CAP_SYS_ADMIN privilege check
        uses: cilium/little-vm-helper@c44c1221b104ee02ec0235211f7ace3c88eb11a2 # v0.0.19
        if: contains(fromJSON('["4.19-20240912.022020", "5.4-20240912.022020"]'), matrix.kernel)
        with:
          provision: 'false'
          cmd: |
            set -euxo pipefail
            uname -a
            cat /etc/issue
            pushd /host
            bash /host/testdata/run_cap_bpf_test.sh "" "CAP_SYS_ADMIN"
            popd
      
      - name: Test RocketMQ
        uses: cilium/little-vm-helper@c44c1221b104ee02ec0235211f7ace3c88eb11a2 # v0.0.19
        with:
          provision: 'false'
          cmd: |
            set -ex
            uname -a
            cat /etc/issue
            pushd /host
            if [ -f "/var/lib/kyanos/btf/current.btf" ]; then  
                bash /host/testdata/test_rocketmq.sh 'sudo /host/kyanos/kyanos $kyanos_log_option --btf /var/lib/kyanos/btf/current.btf'
            else  
                bash /host/testdata/test_rocketmq.sh 'sudo /host/kyanos/kyanos $kyanos_log_option'
            fi
            popd

      - name: Test filter by comm
        uses: cilium/little-vm-helper@c44c1221b104ee02ec0235211f7ace3c88eb11a2 # v0.0.19
        with:
          provision: 'false'
          cmd: |
            set -euxo pipefail
            uname -a
            cat /etc/issue
            pushd /host
            if [ -f "/var/lib/kyanos/btf/current.btf" ]; then
                bash /host/testdata/test_filter_by_comm.sh 'sudo /host/kyanos/kyanos $kyanos_log_option --btf /var/lib/kyanos/btf/current.btf'
            else
                bash /host/testdata/test_filter_by_comm.sh 'sudo /host/kyanos/kyanos $kyanos_log_option'
            fi
            popd

      - name: Test gotls
        uses: cilium/little-vm-helper@c44c1221b104ee02ec0235211f7ace3c88eb11a2 # v0.0.19
        with:
          provision: 'false'
          cmd: |
            set -ex
            uname -a
            cat /etc/issue
            pushd /host
            if [ -f "/var/lib/kyanos/btf/current.btf" ]; then  
                bash /host/testdata/test_gotls.sh 'sudo /host/kyanos/kyanos $kyanos_log_option --btf /var/lib/kyanos/btf/current.btf'
            else  
                bash /host/testdata/test_gotls.sh 'sudo /host/kyanos/kyanos $kyanos_log_option'
            fi
            popd

      - name: Test https
        uses: cilium/little-vm-helper@c44c1221b104ee02ec0235211f7ace3c88eb11a2 # v0.0.19
        with:
          provision: 'false'
          cmd: |
            set -ex
            uname -a
            cat /etc/issue
            pushd /host
            if [ -f "/var/lib/kyanos/btf/current.btf" ]; then  
                bash /host/testdata/test_https.sh 'sudo /host/kyanos/kyanos $kyanos_log_option --btf /var/lib/kyanos/btf/current.btf'
            else  
                bash /host/testdata/test_https.sh 'sudo /host/kyanos/kyanos $kyanos_log_option'
            fi
            popd

      - name: Test side
        uses: cilium/little-vm-helper@c44c1221b104ee02ec0235211f7ace3c88eb11a2 # v0.0.19
        with:
          provision: 'false'
          cmd: |
            set -ex
            uname -a
            cat /etc/issue
            pushd /host
            if [ -f "/var/lib/kyanos/btf/current.btf" ]; then  
                bash /host/testdata/test_side.sh 'sudo /host/kyanos/kyanos $kyanos_log_option --btf /var/lib/kyanos/btf/current.btf'
            else  
                bash /host/testdata/test_side.sh 'sudo /host/kyanos/kyanos $kyanos_log_option'
            fi
            popd

      - name: Test mysql
        uses: cilium/little-vm-helper@c44c1221b104ee02ec0235211f7ace3c88eb11a2 # v0.0.19
        with:
          provision: 'false'
          cmd: |
            set -ex
            uname -a
            cat /etc/issue
            pushd /host
            if [ -f "/var/lib/kyanos/btf/current.btf" ]; then  
                bash /host/testdata/test_mysql.sh 'sudo /host/kyanos/kyanos $kyanos_log_option --btf /var/lib/kyanos/btf/current.btf'
            else  
                bash /host/testdata/test_mysql.sh 'sudo /host/kyanos/kyanos $kyanos_log_option'
            fi
            popd

      - name: Test base
        uses: cilium/little-vm-helper@c44c1221b104ee02ec0235211f7ace3c88eb11a2 # v0.0.19
        with:
          provision: 'false'
          cmd: |
            set -ex
            uname -a
            cat /etc/issue
            if [ -f "/var/lib/kyanos/btf/current.btf" ]; then  
                bash /host/testdata/test_base.sh 'sudo /host/kyanos/kyanos $kyanos_log_option --btf /var/lib/kyanos/btf/current.btf'
            else  
                bash /host/testdata/test_base.sh 'sudo /host/kyanos/kyanos $kyanos_log_option'
            fi

      - name: Test filter by l3/l4 info
        uses: cilium/little-vm-helper@c44c1221b104ee02ec0235211f7ace3c88eb11a2 # v0.0.19
        with:
          provision: 'false'
          cmd: |
            set -ex
            uname -a
            cat /etc/issue
            if [ -f "/var/lib/kyanos/btf/current.btf" ]; then  
                bash /host/testdata/test_filter_by_l4.sh 'sudo /host/kyanos/kyanos $kyanos_log_option --btf /var/lib/kyanos/btf/current.btf'
            else  
                bash /host/testdata/test_filter_by_l4.sh 'sudo /host/kyanos/kyanos $kyanos_log_option'
            fi

      - name: Test kern evt
        uses: cilium/little-vm-helper@c44c1221b104ee02ec0235211f7ace3c88eb11a2 # v0.0.19
        with:
          provision: 'false'
          cmd: |
            set -ex
            uname -a
            cat /etc/issue
            if [ -f "/var/lib/kyanos/btf/current.btf" ]; then  
                bash /host/testdata/test_kern_evt.sh 'sudo /host/kyanos/kyanos $kyanos_log_option --btf /var/lib/kyanos/btf/current.btf'
            else  
                bash /host/testdata/test_kern_evt.sh 'sudo /host/kyanos/kyanos $kyanos_log_option'
            fi

      - name: Test test docker filter by container id
        uses: cilium/little-vm-helper@c44c1221b104ee02ec0235211f7ace3c88eb11a2 # v0.0.19
        with:
          provision: 'false'
          cmd: |
            set -ex
            uname -a
            cat /etc/issue
            if [ -f "/var/lib/kyanos/btf/current.btf" ]; then  
                bash /host/testdata/test_docker_filter_by_container_id.sh 'sudo /host/kyanos/kyanos $kyanos_log_option --btf /var/lib/kyanos/btf/current.btf'
            else  
                bash /host/testdata/test_docker_filter_by_container_id.sh 'sudo /host/kyanos/kyanos $kyanos_log_option'
            fi

      - name: Test test docker filter by container name
        uses: cilium/little-vm-helper@c44c1221b104ee02ec0235211f7ace3c88eb11a2 # v0.0.19
        with:
          provision: 'false'
          cmd: |
            set -ex
            uname -a
            cat /etc/issue
            if [ -f "/var/lib/kyanos/btf/current.btf" ]; then  
                bash /host/testdata/test_docker_filter_by_container_name.sh 'sudo /host/kyanos/kyanos $kyanos_log_option --btf /var/lib/kyanos/btf/current.btf'
            else  
                bash /host/testdata/test_docker_filter_by_container_name.sh 'sudo /host/kyanos/kyanos $kyanos_log_option'
            fi

      - name: Test filter by pid
        uses: cilium/little-vm-helper@c44c1221b104ee02ec0235211f7ace3c88eb11a2 # v0.0.19
        with:
          provision: 'false'
          cmd: |
            set -ex
            uname -a
            cat /etc/issue
            if [ -f "/var/lib/kyanos/btf/current.btf" ]; then  
                bash /host/testdata/test_docker_filter_by_pid.sh 'sudo /host/kyanos/kyanos $kyanos_log_option --btf /var/lib/kyanos/btf/current.btf'
            else  
                bash /host/testdata/test_docker_filter_by_pid.sh 'sudo /host/kyanos/kyanos $kyanos_log_option'
            fi

      - name: Test test containerd filter by container name
        uses: cilium/little-vm-helper@c44c1221b104ee02ec0235211f7ace3c88eb11a2 # v0.0.19
        with:
          provision: 'false'
          cmd: |
            set -ex
            uname -a
            cat /etc/issue
            if [ -f "/var/lib/kyanos/btf/current.btf" ]; then  
                bash /host/testdata/test_containerd_filter_by_container_name.sh 'sudo /host/kyanos/kyanos $kyanos_log_option --btf /var/lib/kyanos/btf/current.btf'
            else  
                bash /host/testdata/test_containerd_filter_by_container_name.sh 'sudo /host/kyanos/kyanos $kyanos_log_option'
            fi

      - name: Test test containerd filter by container id
        uses: cilium/little-vm-helper@c44c1221b104ee02ec0235211f7ace3c88eb11a2 # v0.0.19
        with:
          provision: 'false'
          cmd: |
            set -ex
            uname -a
            cat /etc/issue
            if [ -f "/var/lib/kyanos/btf/current.btf" ]; then  
                bash /host/testdata/test_containerd_filter_by_container_id.sh 'sudo /host/kyanos/kyanos $kyanos_log_option --btf /var/lib/kyanos/btf/current.btf'
            else  
                bash /host/testdata/test_containerd_filter_by_container_id.sh 'sudo /host/kyanos/kyanos $kyanos_log_option'
            fi

      - name: Test redis
        uses: cilium/little-vm-helper@c44c1221b104ee02ec0235211f7ace3c88eb11a2 # v0.0.19
        with:
          provision: 'false'
          cmd: |
            set -ex
            uname -a
            cat /etc/issue
            if [ -f "/var/lib/kyanos/btf/current.btf" ]; then  
                bash /host/testdata/test_redis.sh 'sudo /host/kyanos/kyanos $kyanos_log_option --btf /var/lib/kyanos/btf/current.btf'
            else  
                bash /host/testdata/test_redis.sh 'sudo /host/kyanos/kyanos $kyanos_log_option'
            fi

      - name: Test k8s
        if: ${{ startsWith(matrix.kernel, '6.') }}
        uses: cilium/little-vm-helper@c44c1221b104ee02ec0235211f7ace3c88eb11a2 # v0.0.19
        with:
          provision: 'false'
          cmd: |
            set -ex
            uname -a
            cat /etc/issue
            pushd /host
            bash /host/testdata/run_k8s_test.sh "" 1
            popd
