name: release-test

on:
  workflow_dispatch:
  push:
    branches: [ "prerelease" ]
  pull_request:
    branches: [ "prerelease" ]

permissions:
  contents: read

jobs:

  release-test:
    timeout-minutes: 60
    runs-on: ubuntu-22.04
    steps:

      - name: Remove large directories
        run: |
          df -h
          cd /opt/hostedtoolcache
          find . -mindepth 1 -maxdepth 1 -type d -not -name 'go' -exec rm -rf {} +
          df -h
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4

      - name: Set up Go
        uses: actions/setup-go@3041bf56c941b39c61721a86cd11f3bb1338122a # v5
        with:
          go-version: '1.22.6'
      - name: Run GoReleaser Draft
        uses: goreleaser/goreleaser-action@9ed2f89a662bf1735a48bc8557fd212fa902bebf # v6
        with:
          distribution: goreleaser
          version: latest
          args: release --snapshot --clean --skip=publish -p 1

      - name: Test binary
        run: |
          ls dist/
          mkdir amd64 && tar zxvf dist/kyanos_v0.0.0-next_linux_amd64.tar.gz -C amd64
          mkdir arm64 && tar zxvf dist/kyanos_v0.0.0-next_linux_arm64.tar.gz -C arm64
          file ./amd64/kyanos
          file ./arm64/kyanos
          file ./amd64/kyanos |grep x86-64 |grep 'statically linked'
          file ./arm64/kyanos |grep aarch64 |grep 'statically linked'

      - name: Store Releases
        uses: actions/upload-artifact@6f51ac03b9356f520e9adb1b1b7802705f340c2b # v4
        with:
          name: kyanos_v0.0.0-next_linux_amd64.tar.gz
          path: dist/kyanos_v0.0.0-next_linux_amd64.tar.gz

      - name: Store Releases
        uses: actions/upload-artifact@6f51ac03b9356f520e9adb1b1b7802705f340c2b # v4
        with:
          name: kyanos_v0.0.0-next_linux_arm64.tar.gz
          path: dist/kyanos_v0.0.0-next_linux_arm64.tar.gz
